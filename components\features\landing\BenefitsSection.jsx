import { Card, CardContent } from "@/components/ui/card";

/**
 * Benefits section component highlighting value propositions for different user types
 * @param {Object} props - Component props
 * @param {string} props.title - Section title
 * @param {string} props.description - Section description
 * @param {Array} props.benefits - Array of benefit objects
 */
export default function BenefitsSection({
  title = "Benefits for Every Educator",
  description = "Discover how AI QuizMaker transforms the teaching and learning experience",
  benefits = [
    {
      category: "For Teachers",
      icon: "👩‍🏫",
      items: [
        "Reduce quiz preparation time by 90%",
        "Focus more on teaching, less on admin work",
        "Create diverse, engaging assessments easily",
        "Get instant insights into student understanding",
        "Build a personal question bank for reuse"
      ],
      bgColor: "bg-quiz-primary/5",
      borderColor: "border-quiz-primary/20"
    },
    {
      category: "For Students",
      icon: "🎓",
      items: [
        "Take quizzes on any device, anywhere",
        "Receive immediate feedback on performance",
        "Practice with varied question formats",
        "Track progress over time",
        "Enjoy engaging, interactive assessments"
      ],
      bgColor: "bg-quiz-success/5",
      borderColor: "border-quiz-success/20"
    },
    {
      category: "For Institutions",
      icon: "🏫",
      items: [
        "Standardize assessment quality across classes",
        "Reduce operational costs and paperwork",
        "Improve student engagement and outcomes",
        "Access comprehensive analytics and reports",
        "Support modern, technology-enhanced learning"
      ],
      bgColor: "bg-quiz-warning/5",
      borderColor: "border-quiz-warning/20"
    }
  ]
}) {
  return (
    <section className="py-20 lg:py-32 bg-secondary/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="max-w-3xl mx-auto text-center mb-16">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground mb-6">
            {title}
          </h2>
          <p className="text-lg text-muted-foreground leading-relaxed">
            {description}
          </p>
        </div>

        {/* Benefits Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {benefits.map((benefit, index) => (
            <Card 
              key={index} 
              className={`${benefit.bgColor} ${benefit.borderColor} border-2 hover:shadow-lg transition-all duration-300`}
            >
              <CardContent className="p-8">
                {/* Category Header */}
                <div className="text-center mb-8">
                  <div className="text-5xl mb-4">{benefit.icon}</div>
                  <h3 className="text-2xl font-bold text-foreground">
                    {benefit.category}
                  </h3>
                </div>

                {/* Benefits List */}
                <ul className="space-y-4">
                  {benefit.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start">
                      <div className="flex-shrink-0 w-5 h-5 rounded-full bg-quiz-primary/20 flex items-center justify-center mt-0.5 mr-3">
                        <svg className="w-3 h-3 text-quiz-primary" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <span className="text-foreground leading-relaxed">{item}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Statistics Section */}
        <div className="mt-20 bg-card rounded-2xl p-8 lg:p-12 border border-border/50">
          <div className="text-center mb-12">
            <h3 className="text-2xl lg:text-3xl font-bold text-foreground mb-4">
              Trusted by Educators Across Uzbekistan
            </h3>
            <p className="text-muted-foreground">
              Join the growing community of innovative educators
            </p>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-3xl lg:text-4xl font-bold text-quiz-primary mb-2">500+</div>
              <div className="text-sm text-muted-foreground">Active Teachers</div>
            </div>
            <div className="text-center">
              <div className="text-3xl lg:text-4xl font-bold text-quiz-primary mb-2">10K+</div>
              <div className="text-sm text-muted-foreground">Quizzes Created</div>
            </div>
            <div className="text-center">
              <div className="text-3xl lg:text-4xl font-bold text-quiz-primary mb-2">50K+</div>
              <div className="text-sm text-muted-foreground">Students Engaged</div>
            </div>
            <div className="text-center">
              <div className="text-3xl lg:text-4xl font-bold text-quiz-primary mb-2">98%</div>
              <div className="text-sm text-muted-foreground">Satisfaction Rate</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
