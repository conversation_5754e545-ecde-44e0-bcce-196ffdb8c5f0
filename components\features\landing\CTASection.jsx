import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import Link from "next/link";

/**
 * Call-to-Action section component for the landing page
 * @param {Object} props - Component props
 * @param {string} props.title - Main CTA title
 * @param {string} props.description - CTA description
 * @param {string} props.primaryCTA - Primary button text
 * @param {string} props.secondaryCTA - Secondary button text
 * @param {string} props.primaryCTALink - Primary button link
 * @param {string} props.secondaryCTALink - Secondary button link
 */
export default function CTASection({
  title = "Ready to Transform Your Teaching?",
  description = "Join thousands of educators who are already creating better quizzes with AI. Start your free trial today and experience the future of assessment.",
  primaryCTA = "Start Free Trial",
  secondaryCTA = "Schedule Demo",
  primaryCTALink = "/register",
  secondaryCTALink = "/demo",
  features = [
    "✅ No credit card required",
    "✅ 14-day free trial",
    "✅ Full feature access",
    "✅ Cancel anytime"
  ]
}) {
  return (
    <section className="py-20 lg:py-32 bg-gradient-to-br from-quiz-primary/5 via-background to-quiz-primary/10">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <Card className="border-2 border-quiz-primary/20 shadow-2xl bg-card/80 backdrop-blur-sm">
            <CardContent className="p-8 lg:p-12 text-center">
              {/* Main CTA Content */}
              <div className="mb-10">
                <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground mb-6 leading-tight">
                  {title}
                </h2>
                <p className="text-lg text-muted-foreground leading-relaxed max-w-2xl mx-auto">
                  {description}
                </p>
              </div>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-10">
                <Button asChild size="lg" className="w-full sm:w-auto text-lg px-8 py-6">
                  <Link href={primaryCTALink}>
                    {primaryCTA}
                  </Link>
                </Button>
                <Button asChild variant="outline" size="lg" className="w-full sm:w-auto text-lg px-8 py-6">
                  <Link href={secondaryCTALink}>
                    {secondaryCTA}
                  </Link>
                </Button>
              </div>

              {/* Features List */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 max-w-3xl mx-auto mb-10">
                {features.map((feature, index) => (
                  <div key={index} className="text-sm text-muted-foreground">
                    {feature}
                  </div>
                ))}
              </div>

              {/* Trust Indicators */}
              <div className="border-t border-border/50 pt-8">
                <p className="text-sm text-muted-foreground mb-4">
                  Trusted by leading educational institutions
                </p>
                <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
                  {/* Placeholder for institution logos */}
                  <div className="text-xs text-muted-foreground px-4 py-2 border border-border/30 rounded-lg">
                    Tashkent International School
                  </div>
                  <div className="text-xs text-muted-foreground px-4 py-2 border border-border/30 rounded-lg">
                    Westminster International University
                  </div>
                  <div className="text-xs text-muted-foreground px-4 py-2 border border-border/30 rounded-lg">
                    Inha University Tashkent
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-quiz-primary/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-quiz-success/10 rounded-full blur-3xl"></div>
      </div>
    </section>
  );
}

/**
 * Footer component for the landing page
 */
export function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-foreground/5 border-t border-border/50 py-12">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="col-span-1 md:col-span-2">
            <h3 className="text-xl font-bold text-foreground mb-4">AI QuizMaker</h3>
            <p className="text-muted-foreground mb-4 max-w-md">
              Empowering educators in Uzbekistan with AI-powered quiz creation tools. 
              Create, manage, and deliver exceptional assessments with ease.
            </p>
            <div className="text-sm text-muted-foreground">
              Made with ❤️ for Uzbekistan's educators
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="font-semibold text-foreground mb-4">Quick Links</h4>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li><Link href="/features" className="hover:text-quiz-primary transition-colors">Features</Link></li>
              <li><Link href="/pricing" className="hover:text-quiz-primary transition-colors">Pricing</Link></li>
              <li><Link href="/demo" className="hover:text-quiz-primary transition-colors">Demo</Link></li>
              <li><Link href="/support" className="hover:text-quiz-primary transition-colors">Support</Link></li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h4 className="font-semibold text-foreground mb-4">Contact</h4>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li>Email: <EMAIL></li>
              <li>Phone: +998 (71) 123-4567</li>
              <li>Tashkent, Uzbekistan</li>
            </ul>
          </div>
        </div>

        <div className="border-t border-border/50 mt-8 pt-8 text-center text-sm text-muted-foreground">
          <p>&copy; {currentYear} AI QuizMaker. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
}
