Migrating to Auth.js (formerly NextAuth.js v5) involves several key changes, especially with the Next.js App Router. Here's a comprehensive guide to help you integrate the latest version into your JavaScript Next.js project without using the Prisma adapter.

**Key Changes in Auth.js (v5):**

- **App Router First:** While `pages/` is still supported, v5 is designed with the App Router in mind.
- **Simplified Setup:** Configuration is more centralized.
- **Universal `auth()` Method:** A single `auth()` method replaces `getServerSession`, `getSession`, `withAuth`, `getToken`, and `useSession`.
- **Edge Compatibility:** Improved support for Edge functions.
- **Environment Variable Prefix:** Environment variables are now typically prefixed with `AUTH_` instead of `NEXTAUTH_`. Some documentation might still show `NEXTAUTH_`, but `AUTH_` is the newer convention for auto-detection.
- **Adapter Packages:** Database adapter packages have moved from `@next-auth/*-adapter` to `@auth/*-adapter`. Since you are not using Prisma, this change is less critical unless you opt for another database adapter later. By default, Auth.js will use JWT for session management if no adapter is specified.

**Migration and Integration Steps:**

**1. Installation:**

Install the latest version of `next-auth`. It's currently in beta, so the `@beta` tag is used.

```bash
npm install next-auth@beta
# or
pnpm add next-auth@beta
# or
yarn add next-auth@beta
```

**2. Environment Variables:**

Create or update your `.env.local` file in the root of your project. You'll need at least `AUTH_SECRET`.

```
AUTH_SECRET="YOUR_GENERATED_SECRET_HERE"
# Add credentials for your chosen OAuth providers
# Example for GitHub:
AUTH_GITHUB_ID="YOUR_GITHUB_CLIENT_ID"
AUTH_GITHUB_SECRET="YOUR_GITHUB_CLIENT_SECRET"

# Example for Google:
AUTH_GOOGLE_ID="YOUR_GOOGLE_CLIENT_ID"
AUTH_GOOGLE_SECRET="YOUR_GOOGLE_CLIENT_SECRET"
```

You can generate a suitable secret using OpenSSL: `openssl rand -hex 32`.

**3. Create the Auth.js Configuration File:**

Create a file named `auth.js` (or `auth.config.js` if you need to separate it for middleware, but for a simpler setup without an edge-incompatible adapter, `auth.js` is fine) in the root of your project (or inside `src/` if you are using an `src` directory).

This file will contain your Auth.js configuration, including providers. Since you are not using a Prisma adapter, Auth.js will default to using JWT for session management (`strategy: "jwt"` is the default when no adapter is provided).

```javascript
// auth.js
import NextAuth from "next-auth";
import GitHub from "next-auth/providers/github";
import Google from "next-auth/providers/google";
// Import other providers as needed (e.g., Credentials)

export const { handlers, auth, signIn, signOut } = NextAuth({
  providers: [
    GitHub({
      clientId: process.env.AUTH_GITHUB_ID,
      clientSecret: process.env.AUTH_GITHUB_SECRET,
    }),
    Google({
      clientId: process.env.AUTH_GOOGLE_ID,
      clientSecret: process.env.AUTH_GOOGLE_SECRET,
    }),
    // Example for Credentials provider (if you want email/password)
    // Credentials({
    //   name: "Credentials",
    //   credentials: {
    //     username: { label: "Username", type: "text", placeholder: "jsmith" },
    //     password: { label: "Password", type: "password" }
    //   },
    //   async authorize(credentials) {
    //     // Add your logic here to look up the user from the credentials supplied
    //     const user = { id: "1", name: "J Smith", email: "<EMAIL>" }; // Replace with your user validation
    //
    //     if (user) {
    //       // Any object returned will be saved in `user` property of the JWT
    //       return user;
    //     } else {
    //       // If you return null then an error will be displayed advising the user to check their details.
    //       return null;
    //
    //       // You can also Reject this callback with an Error thus the user will be sent to the error page with the error message as a query parameter
    //     }
    //   }
    // })
  ],
  // session: { strategy: "jwt" }, // JWT is the default when no adapter is specified
  // pages: { // Optional: Customize sign-in, sign-out, error pages
  //   signIn: '/auth/signin',
  //   // signOut: '/auth/signout',
  //   // error: '/auth/error', // Error code passed in query string as ?error=
  //   // verifyRequest: '/auth/verify-request', // (used for email/passwordless login)
  //   // newUser: '/auth/new-user' // New users will be directed here on first sign in (leave the property out to disable)
  // },
  // callbacks: { // Optional: Customize JWT and session data
  //   async jwt({ token, user, account }) {
  //     // Persist the OAuth access_token to the token right after signin
  //     if (account && user) {
  //       token.accessToken = account.access_token;
  //       token.id = user.id; // Persist user ID to the token
  //     }
  //     return token;
  //   },
  //   async session({ session, token }) {
  //     // Send properties to the client, like an access_token and user ID from the token.
  //     session.accessToken = token.accessToken;
  //     session.user.id = token.id; // Add user ID to the session
  //     return session;
  //   },
  // }
});
```

**4. Create the API Route Handler:**

In your `app` directory, create the API route handler for Auth.js. This handles requests to `/api/auth/*`.

Create the file `app/api/auth/[...nextauth]/route.js`:

```javascript
// app/api/auth/[...nextauth]/route.js
export { GET, POST } from "@/auth"; // Adjust the path to your auth.js file if it's not in the root
// If your auth.js is in the root, it would be:
// import { handlers } from "../../../auth" // Adjust path based on your project structure
// export const { GET, POST } = handlers
```

**5. Session Provider (Client-Side):**

To use session data in client components, you need to wrap your application (or relevant parts of it) with `SessionProvider`. Since `SessionProvider` uses React Context, it needs to be in a Client Component.

Create a client component, for example, `app/components/Providers.jsx`:

```javascript
// app/components/Providers.jsx
"use client";

import { SessionProvider } from "next-auth/react";

export default function Providers({ children, session }) {
  return <SessionProvider session={session}>{children}</SessionProvider>;
}
```

Then, use this `Providers` component in your root `layout.js`. You'll fetch the session server-side in the layout and pass it to the provider.

```javascript
// app/layout.js
import Providers from "./components/Providers"; // Adjust path as needed
import { auth } from "@/auth"; // Adjust path to your auth.js file

export default async function RootLayout({ children }) {
  const session = await auth(); // Get session on the server
  return (
    <html lang="en">
      <body>
        <Providers session={session}>
          {" "}
          {/* Pass session to the provider */}
          {children}
        </Providers>
      </body>
    </html>
  );
}
```

**6. Accessing Session Data:**

- **Client Components:**
  Use the `useSession` hook from `next-auth/react`.

  ```javascript
  // app/some-client-component.jsx
  "use client";

  import { useSession, signIn, signOut } from "next-auth/react";

  export default function Component() {
    const { data: session, status } = useSession();

    if (status === "loading") {
      return <p>Loading...</p>;
    }

    if (session) {
      return (
        <>
          Signed in as {session.user.email} <br />
          <button onClick={() => signOut()}>Sign out</button>
        </>
      );
    }
    return (
      <>
        Not signed in <br />
        <button onClick={() => signIn("github")}>Sign in with GitHub</button>
      </>
    );
  }
  ```

- **Server Components & Route Handlers:**
  Use the `auth` function exported from your `auth.js` file.

  ```javascript
  // app/some-server-page/page.jsx
  import { auth } from "@/auth"; // Adjust path

  export default async function Page() {
    const session = await auth();

    if (!session) {
      // Redirect or show login link
      return <p>Please sign in.</p>;
    }

    return <p>Hello, {session.user.name}!</p>;
  }
  ```

**7. Protecting Routes with Middleware:**

Create a `middleware.js` file in the root of your project (or `src/` if applicable).

```javascript
// middleware.js
export { auth as middleware } from "@/auth"; // Adjust path

// Optionally, you can export a `config` object to specify which routes the middleware should apply to.
// See: https://nextjs.org/docs/app/building-your-application/routing/middleware#matcher
// export const config = {
//   matcher: ["/dashboard/:path*", "/profile"],
// };
```

If you have separated `auth.config.js` (e.g., for providers) and `auth.js` (e.g., for an adapter not compatible with edge), your middleware would import from `auth.config.js`. However, since you're not using an adapter like Prisma, you might not need this separation unless you encounter edge compatibility issues with a specific provider. If you _only_ have `auth.js` containing everything, you can directly use it.

**Important Considerations When Not Using an Adapter (JWT Strategy):**

- **Session Data:** User information (like name, email, etc., and any custom data you add in callbacks) is stored in a JWT.
- **Database Interaction:** You are responsible for any database interactions if you need to store persistent user data beyond what's in the JWT (e.g., application-specific user profiles, roles, permissions). This would typically be done in the `signIn` or `jwt` callbacks using your own database client.
- **Security of JWT Secret:** The `AUTH_SECRET` is crucial for signing and encrypting JWTs. Keep it secure.

**Summary of Key File Structure:**

```
.
├── app/
│   ├── api/
│   │   └── auth/
│   │       └── [...nextauth]/
│   │           └── route.js       # Handles auth API calls
│   ├── components/
│   │   └── Providers.jsx        # Client component for SessionProvider
│   ├── layout.js                # Root layout, includes Providers
│   └── page.js                  # Your main page
├── auth.js                      # Auth.js configuration (providers, etc.)
├── middleware.js                # Optional: For protecting routes
├── .env.local                   # Environment variables (AUTH_SECRET, provider creds)
└── package.json
```

This guide provides a general approach. You'll need to adapt provider configurations and any custom callback logic to your specific needs. Always refer to the official Auth.js documentation for the most up-to-date information and advanced configurations.
