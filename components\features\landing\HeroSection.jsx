import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";

/**
 * Hero section component for the landing page
 * @param {Object} props - Component props
 * @param {string} props.title - Main hero title
 * @param {string} props.subtitle - Hero subtitle/description
 * @param {string} props.primaryCTA - Primary call-to-action text
 * @param {string} props.secondaryCTA - Secondary call-to-action text
 * @param {string} props.primaryCTALink - Primary CTA link
 * @param {string} props.secondaryCTALink - Secondary CTA link
 */
export default function HeroSection({
  title = "AI-Powered Quiz Creation for Modern Educators",
  subtitle = "Transform your teaching with intelligent quiz generation. Create, manage, and deliver engaging assessments in minutes, not hours.",
  primaryCTA = "Start Creating Quizzes",
  secondaryCTA = "Watch Demo",
  primaryCTALink = "/register",
  secondaryCTALink = "#demo"
}) {
  return (
    <section className="relative bg-gradient-to-br from-background via-background to-secondary/20 py-20 lg:py-32">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          {/* Hero Badge */}
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-8 animate-fade-in">
            <span className="w-2 h-2 bg-quiz-primary rounded-full mr-2"></span>
            Designed for Uzbekistan's Educational Excellence
          </div>

          {/* Main Heading */}
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground mb-6 leading-tight animate-slide-up">
            {title}
          </h1>

          {/* Subtitle */}
          <p className="text-lg sm:text-xl text-muted-foreground mb-10 max-w-2xl mx-auto leading-relaxed animate-slide-up">
            {subtitle}
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12 animate-slide-up">
            <Button asChild size="lg" className="w-full sm:w-auto">
              <Link href={primaryCTALink}>
                {primaryCTA}
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="w-full sm:w-auto">
              <Link href={secondaryCTALink}>
                {secondaryCTA}
              </Link>
            </Button>
          </div>

          {/* Hero Stats */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-2xl mx-auto animate-fade-in">
            <div className="text-center">
              <div className="text-2xl font-bold text-quiz-primary mb-2">10x</div>
              <div className="text-sm text-muted-foreground">Faster Quiz Creation</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-quiz-primary mb-2">5+</div>
              <div className="text-sm text-muted-foreground">Question Types</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-quiz-primary mb-2">100%</div>
              <div className="text-sm text-muted-foreground">Auto-Graded</div>
            </div>
          </div>
        </div>
      </div>

      {/* Background Decoration */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary/5 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-quiz-primary/5 rounded-full blur-3xl"></div>
      </div>
    </section>
  );
}
