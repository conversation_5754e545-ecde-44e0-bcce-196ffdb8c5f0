Project Architecture Document: AI-Powered QuizMaker
Version: 0.1
Date: May 21, 2025
Lead: AI Technical Leader (guided by User Developer)
Table of Contents
Introduction
1.1. Purpose of this Document
1.2. Project Overview
1.3. Goals of the Architecture
1.4. Guiding Principles
1.5. Constraints
Technology Stack
2.1. Frontend
2.2. Backend (Next.js Full-Stack Approach)
2.3. Database
2.4. AI Integration
2.5. Key Libraries & Tools (Styling, State Management, Forms, Data Fetching, Auth, PDF Gen, etc.)
Architectural Overview & Style
3.1. High-Level Diagram
3.2. Architectural Style
3.3. Key Design Decisions & Rationale
Frontend Architecture (Next.js App Router)
4.1. Directory Structure
4.2. Component Strategy
4.3. State Management
4.4. Data Fetching (Client-Side)
4.5. Routing (Next.js App Router)
4.6. Forms
4.7. Styling
4.8. Authentication (NextAuth.js)
Backend Architecture (Next.js: Route Handlers & Server Actions)
5.1. API Design
5.2. Route Handlers (app/\*\*/route.js)
5.3. Server Actions
5.4. AI Integration (Gemini API)
5.5. PDF Generation (Puppeteer)
5.6. Business Logic
Database Architecture
6.1. Choice of Database (PostgreSQL)
6.2. ORM (Prisma)
6.3. Data Modeling & Schema (schema.prisma)
Security Considerations
7.1. Authentication & Authorization
7.2. Input Validation
7.3. Output Encoding (Mitigating Cross-Site Scripting - XSS)
7.4. API Security (Protecting Route Handlers & Server Actions)
7.5. Secret Management
7.6. CSRF (Cross-Site Request Forgery) Protection
7.7. Error Handling & Information Exposure
7.8. Dependency Management
7.9. HTTPS
7.10. Database Security
Deployment Architecture (Initial Plan for MVP)
8.1. Target Platform(s)
8.2. Build Process
8.3. Environment Variables Management
8.4. CI/CD (Continuous Integration / Continuous Deployment)
8.5. Domain & DNS
Performance Considerations
9.1. Frontend Performance
9.2. Backend Performance (Next.js Route Handlers & Server Actions)
9.3. Caching Strategies
9.4. Monitoring & Optimization (Post-MVP Planning)
Error Handling & Logging
10.1. Frontend Error Handling
10.2. Backend Error Handling (Next.js Route Handlers & Server Actions)
10.3. Logging Strategy (MVP)
Scalability (Future Considerations)
11.1. Frontend Scalability (Next.js on Vercel)
11.2. Backend Scalability (Next.js Serverless Functions on Vercel)
11.3. Database Scalability (Neon - Serverless PostgreSQL)
11.4. AI Integration (Gemini API)
11.5. Caching
11.6. Asynchronous Processing (Future Enhancement)
Development Practices & Conventions (MVP Focus)
12.1. Naming Conventions
12.2. Code Commenting & Documentation
12.3. Code Formatting & Linting
12.4. Git Workflow
12.5. Manual Testing Strategy for MVP
12.6. Environment Management
12.7. Dependency Management
Risks & Mitigation (Technical)
13.1. Risk: Runtime Errors and Reduced Maintainability due to Lack of TypeScript
13.2. Risk: Regressions and Higher Bug Rate due to Lack of Automated Tests
13.3. Risk: Performance Bottlenecks with AI/PDF Generation in Serverless Functions
13.4. Risk: Security Vulnerabilities
13.5. Risk: Scalability Limits of MVP Architecture (Long-Term)
13.6. Risk: Vendor Lock-in (e.g., Vercel, Neon)

1. Introduction
   **1.1. Purpose of this Document**
   This document outlines the software architecture for the AI-Powered QuizMaker web application. It details the architectural design, technology stack, key components, design decisions, and guiding principles. Its purpose is to provide a clear technical blueprint for the development team (initially the User Developer), ensure alignment with project goals, and serve as a reference for future development and scaling efforts.

**1.2. Project Overview**
The AI-Powered QuizMaker is a web application designed to assist educators in private schools and tutoring centers in Uzbekistan. It enables them to efficiently create (manually or via AI using PDF content/topic inputs), administer, and automatically grade quizzes. The application supports various delivery modes, including online assessments (for homework or in-class computer labs), printable PDF quizzes with anti-cheating variations, and an interactive classroom display mode. The core objective is to save educators time, enhance student engagement, and provide insights into student performance. The Minimum Viable Product (MVP) will focus on core functionalities with fully automated grading.
_(Reference: See PRD Version 0.2 for full product details)._

**1.3. Goals of the Architecture**
The primary goals for the AI-Powered QuizMaker's architecture are:

- **Rapid MVP Development:** Enable quick iteration and delivery of core features by leveraging the developer's existing expertise and modern, productive frameworks.
- **User-Centric Design:** Support the creation of an intuitive and efficient user experience for both educators and students, as outlined in the PRD.
- **Maintainability (within MVP constraints):** Strive for a clear and understandable codebase, even without TypeScript for the MVP, through good structure and conventions.
- **Scalability (Foundation for Future):** While the MVP will be a monolithic Next.js application, foundational choices (e.g., database, ORM) should allow for future scaling as the user base and feature set grow.
- **Reliability:** Ensure the application performs its core functions dependably.
- **Leverage Modern Features:** Utilize appropriate modern features of the Next.js framework to enhance development efficiency and application capabilities.

**1.4. Guiding Principles**
The following principles will guide architectural and technical decisions for the MVP:

- **MVP First:** Prioritize simplicity and the delivery of core PRD features for the initial version.
- **Full-Stack Next.js:** Maximize the use of Next.js for both frontend and backend capabilities to streamline development.
- **Embrace External Packages:** Utilize reliable, well-maintained external packages for common functionalities (e.g., authentication, UI components, state management, form handling) to accelerate development.
- **Developer Productivity:** Choose tools and patterns that enhance the developer's productivity and align with their existing skill set.
- **Clear Structure:** Maintain a well-organized codebase with clear separation of concerns where appropriate within the Next.js paradigm.
- **Plan for Iteration:** Acknowledge that the MVP is a starting point, and the architecture should be adaptable for future enhancements and refactoring.

**1.5. Constraints**
The architecture and development of the MVP will operate under the following explicit constraints:

- **JavaScript Only:** The primary programming language will be JavaScript. TypeScript will not be implemented for the MVP.
- **No Automated Tests (for MVP):** Automated testing (unit, integration, E2E) will be deferred for the MVP. Emphasis will be placed on thorough manual testing.
- **Sole Developer (Initially):** The initial development will be undertaken by a single developer.
- **Focus on Auto-Grading:** All question types supported in the MVP must be automatically gradable.
  Use code with caution.

2. Technology Stack
   This section details the primary technologies, frameworks, libraries, and tools selected for the development of the AI-Powered QuizMaker MVP.
   **2.1. Frontend**

- **Framework:** Next.js (Version 14+ with App Router)
  - _Rationale:_ Provides a robust full-stack React framework enabling rapid development, Server Components, Client Components, efficient routing, and a rich feature set. Aligns with developer expertise.
- **UI Library:** React.js
  - _Rationale:_ Core library for building interactive user interfaces, leveraged by Next.js.
- **Programming Language:** JavaScript (ES6+)
  - _Rationale:_ Developer's choice for MVP to expedite initial development.
- **Styling:**
  - **Tailwind CSS:** Utility-first CSS framework for rapid UI development and consistent styling.
  - **Shadcn/UI:** Collection of beautifully designed, accessible, and customizable UI components built on Tailwind CSS and Radix UI. Components are directly integrated into the project for full control.
  - _Rationale:_ Offers a modern, efficient, and highly customizable approach to styling and UI construction.
- **State Management (Client-Side Global):** Zustand
  - _Rationale:_ Simple, fast, and scalable state management solution with minimal boilerplate, suitable for managing global UI state not handled by React Query.
- **Data Fetching & Caching (Client-Side Server State):** React Query (TanStack Query v5)
  - _Rationale:_ Powerful library for managing server state, handling data fetching, caching, synchronization, and updates with excellent developer experience.
- **HTTP Client (for React Query):** Axios
  - _Rationale:_ Mature, promise-based HTTP client for making requests from client-side components (via React Query) to backend Route Handlers. Developer preference.
- **Form Handling:** React Hook Form
  - _Rationale:_ Performant, flexible, and easy-to-use library for managing form state, validation, and submissions.
- **Routing:** Next.js App Router
  - _Rationale:_ Integrated routing solution within Next.js, supporting layouts, nested routes, loading UI, and error handling.

**2.2. Backend (Next.js Full-Stack Approach)**

- **Framework:** Next.js (App Router - Route Handlers & Server Actions)
  - _Rationale:_ Utilizes Next.js's backend capabilities for API endpoint creation (Route Handlers) and server-side mutations (Server Actions where applicable) to maintain a unified full-stack JavaScript environment.
- **Programming Language:** JavaScript (ES6+)
- **Runtime Environment:** Node.js (as leveraged by Next.js)
- **Authentication:** NextAuth.js (Auth.js v5)
  - _Rationale:_ Comprehensive and secure authentication solution specifically designed for Next.js, simplifying the implementation of various authentication strategies.

**2.3. Database**

- **Database System:** PostgreSQL (Version 15+)
  - _Rationale:_ Robust, feature-rich, and highly scalable open-source relational database, suitable for structured data and complex relationships as required by the application.
- **ORM (Object-Relational Mapper):** Prisma (Version 5+)
  - _Rationale:_ Modern database toolkit for Node.js and TypeScript (used here with JavaScript). Provides a type-safe query builder (benefit partially reduced without TS but still valuable for query construction), an intuitive schema migration system, and Prisma Studio for data management.

**2.4. AI Integration**

- **AI Service:** Google Gemini API
  - _Rationale:_ Chosen AI service for generating quiz content based on PDF uploads or topic inputs, as per PRD.
- **API Interaction:** Via backend Route Handlers using `axios` (or native `fetch`) for secure server-to-server communication.

**2.5. Key Libraries & Tools (General / Cross-Cutting)**

- **PDF Generation:** Puppeteer
  - _Rationale:_ Node.js library to control a headless Chrome/Chromium instance, enabling robust HTML-to-PDF conversion for generating printable quizzes and answer keys. Offers high fidelity for complex layouts.
- **Linting:** ESLint (with Next.js base configuration, React recommended, JSX-A11y, React Hooks plugins)
  - _Rationale:_ Essential for maintaining code quality, catching common errors, and enforcing coding standards, especially in a JavaScript-only project.
- **Formatting:** Prettier
  - _Rationale:_ Opinionated code formatter to ensure consistent code style across the project.
- **Utility Libraries (Examples):**
  - `date-fns` or `Day.js`: For lightweight date/time manipulation.
  - `clsx`: For conditionally constructing Tailwind CSS class strings (often included with Shadcn/UI).
  - `JSDoc`: For code commenting and pseudo-type hinting in JavaScript.
- **Version Control:** Git (with a platform like GitHub, GitLab, or Bitbucket) \* _Rationale:_ Standard for source code management, collaboration, and version history.
  Use code with caution.

3. Architectural Overview & Style
   **3.1. High-Level Diagram**
   _(This section requires a visual diagram. A description is provided below for creating the diagram using a tool like diagrams.net (draw.io), Lucidchart, Miro, or Excalidraw)._

**Diagram Description:**
The diagram should depict the following key components and flows:

- **User (Browser):** Interacts with the Next.js Frontend.
- **Next.js Application (Deployed on Vercel or similar):** This is the central monolith.
  - **Frontend (App Router - Client Components & Server Components):**
    - Receives user input.
    - Renders UI (Shadcn/UI, Tailwind CSS).
    - Manages client-side state (Zustand, React Query).
    - Handles forms (React Hook Form).
    - Initiates client-side data requests to Next.js Route Handlers (via React Query & Axios).
    - Makes calls to Server Actions.
  - **Backend (App Router - Route Handlers & Server Actions):**
    - Handles API requests from the frontend.
    - Manages Authentication (NextAuth.js).
    - Contains Business Logic (quiz management, user interactions).
    - Interacts with the PostgreSQL Database via Prisma.
    - Calls the external Gemini AI API (securely from the backend).
    - Handles PDF Generation (Puppeteer).
- **PostgreSQL Database (Managed Service - Neon):**
  - Stores all persistent data (users, quizzes, questions, classes, submissions).
  - Accessed by the Next.js backend via Prisma.
- **Gemini AI API (External Service):**
  - Receives content (PDF data, topics) from the Next.js backend.
  - Returns generated quiz questions.
- **NextAuth.js:** Sits within the Next.js application, handling authentication flows, session management, and interactions with credential storage.

**Key Interactions to Illustrate:**

- User login/registration flow (Browser -> Next.js Frontend -> NextAuth.js -> Next.js Backend -> DB).
- Quiz creation (AI-based): (Browser -> Next.js Frontend -> Next.js Backend -> Gemini API -> Next.js Backend -> DB).
- Quiz taking (online): (Browser -> Next.js Frontend -> Next.js Backend -> DB).
- PDF quiz generation: (Browser -> Next.js Frontend -> Next.js Backend -> Puppeteer -> PDF Download).

**3.2. Architectural Style**
The AI-Powered QuizMaker MVP will employ a **Monolithic Next.js Application** architecture, leveraging the **App Router paradigm**.

- **Monolithic Approach:** For the MVP, both frontend and backend logic will reside within a single Next.js application.
  - _Rationale:_ Simplifies development, deployment, and project management for a solo developer and a focused MVP scope. Reduces initial operational overhead.
- **Component-Based UI:** The frontend will be built using React's component-based architecture, promoting reusability and modularity.
- **Server-Client Model within Next.js App Router:**
  - **Server Components:** Used by default for rendering UI that can be generated on the server, fetching data directly, and reducing client-side JavaScript.
  - **Client Components:** Used for interactive UI elements, leveraging browser APIs, and managing client-side state.
  - **Route Handlers:** Serve as the primary mechanism for creating dedicated backend API endpoints, handling CRUD operations, and interacting with external services or the database.
  - **Server Actions:** Will be considered for specific mutation operations directly from components where it simplifies the code and improves user experience (e.g., form submissions), provided they align with stable Next.js features.
- **Service Layer (Conceptual):** While not strictly enforced by a framework, backend logic within Route Handlers or Server Actions will be organized into helper/service functions where appropriate to promote separation of concerns and reusability (e.g., a `quizService.js`, `aiService.js`). These will reside in the `lib/` directory.

**3.3. Key Design Decisions & Rationale**

- **Decision: Full-Stack Next.js with App Router.**
  - _Rationale:_ Leverages developer's existing Next.js expertise, provides a modern and productive environment, and simplifies the stack for MVP by co-locating frontend and backend concerns. The App Router offers powerful features like Server Components, layouts, and improved data fetching patterns.
- **Decision: JavaScript only for MVP.**
  - _Rationale:_ Developer choice to expedite initial feature delivery by deferring the adoption of TypeScript. This decision accepts the trade-off of reduced static type safety for perceived higher initial velocity. Mitigation will involve diligent JSDoc and linting.
- **Decision: Deferral of Automated Tests for MVP.**
  - _Rationale:_ To accelerate initial development. This decision accepts the risk of regressions and increased debugging time. Mitigation will involve thorough manual testing of all user flows.
- **Decision: PostgreSQL (via Neon) with Prisma.**
  - _Rationale:_ PostgreSQL offers robust relational data management. Neon provides a serverless, scalable PostgreSQL experience. Prisma provides an intuitive and powerful ORM that simplifies database interactions, migrations, and schema management.
- **Decision: NextAuth.js for Authentication.**
  - _Rationale:_ Provides a secure, flexible, and well-integrated solution for handling authentication within Next.js applications, significantly reducing the effort to implement robust auth flows (Credentials & Google Provider for MVP). JWT sessions will be used.
- **Decision: Zustand for Global Client State & React Query for Server State.**
  - _Rationale:_ Clear separation of concerns for state. React Query excels at managing asynchronous server state. Zustand provides a lightweight solution for purely client-side global UI state.
- **Decision: External `components/` directory.**
  - _Rationale:_ Common practice for organizing globally shared React components, keeping them separate from the route-specific `app/` directory structure for better modularity.
- **Decision: Puppeteer for PDF Generation.** \* _Rationale:_ Offers high-fidelity HTML-to-PDF conversion, allowing complex and well-styled layouts for printable quizzes to be defined using familiar web technologies.
  Use code with caution.

4. Frontend Architecture (Next.js App Router)
   The frontend architecture is built upon Next.js (Version 14+ with App Router) and React, focusing on a component-based approach with a clear separation between server-rendered UI and client-side interactivity.
   **4.1. Directory Structure**
   A well-organized directory structure is crucial for maintainability. The proposed top-level structure is as follows:

- `app/`: Core of the Next.js App Router. Contains all route definitions, layouts, pages, loading UIs, error boundaries, and Route Handlers.
  - `(auth)/`: Route group for authentication-related pages (e.g., login, register) with its own layout.
  - `(dashboard)/`: Route group for protected teacher/admin dashboard routes, with its own layout.
    - `quizzes/`
      - `[quizId]/`
        - `edit/page.js`
        - `page.js` (view quiz details)
      - `create/page.js`
      - `page.js` (list quizzes)
    - `students/`
    - `reports/`
  - `(student)/`: Route group for protected student-facing routes (e.g., taking a quiz).
  - `api/`: Conventional location for Route Handlers.
    - `auth/[...nextauth]/route.js` (NextAuth.js handler)
    - `quiz/route.js`
    - `gemini/route.js`
  - `layout.js` (Root layout)
  - `page.js` (Homepage)
- `components/`: Global, reusable UI components, not specific to any single route.
  - `ui/`: (Shadcn/UI components will reside here after being added via CLI)
  - `common/`: Application-specific common components (e.g., `Button.js`, `Modal.js`, `LoadingSpinner.js`).
  - `quiz/`: Components specific to quiz display or interaction (e.g., `QuestionCard.js`, `QuizTimer.js`).
  - `layout/`: Components used in main layouts (e.g., `Navbar.js`, `Sidebar.js`).
- `lib/`: Utility functions, helper scripts, third-party library configurations, Prisma client instance.
  - `prisma.js` (Prisma client instantiation and export)
  - `axios.js` (Axios instance configuration, if needed)
  - `utils.js` (General utility functions)
  - `authOptions.js` (Configuration for NextAuth.js if extensive)
- `hooks/`: Custom React hooks (e.g., `useQuizTimer.js`).
- `store/`: Zustand store definitions.
  - `uiStore.js`
  - `quizCreationStore.js`
- `styles/`: Global styles, Tailwind CSS configuration.
  - `globals.css`
  - `tailwind.config.js`
- `public/`: Static assets (images, fonts, etc.).

**4.2. Component Strategy**

- **Server Components (Default in `app/` directory):**
  - Used for UI that can be rendered on the server, data fetching directly from backend sources.
- **Client Components (`'use client';` directive):**
  - Used for interactive UI elements, event handling, managing local component state, and lifecycle effects.
- **Shadcn/UI:**
  - Components added via CLI into `components/ui/` for full customization.

**4.3. State Management**

- **Local Component State:** `useState` and `useReducer`.
- **Global Client State (Zustand):** For shared state across unrelated Client Components.
- **Server Cache / Async State (React Query):** See section 4.4.

**4.4. Data Fetching (Client-Side for Dynamic Interactions)**

- **React Query (TanStack Query v5):** Primary tool for managing asynchronous operations and server state on the client side.
- **Axios:** HTTP client function passed to React Query.

**4.5. Routing (Next.js App Router)**

- Conventions: `page.js`, `layout.js`, `loading.js`, `error.js`, dynamic routes, route groups.

**4.6. Forms**

- **React Hook Form:** For form state, submissions, and client-side validation.
- **Validation:** React Hook Form's built-in capabilities or custom validation functions. Server-side validation remains critical.

**4.7. Styling**

- **Tailwind CSS:** Utility classes in JSX.
- **Shadcn/UI:** As styled primitives.
- **Global Styles:** `styles/globals.css` for base styles.

**4.8. Authentication (NextAuth.js Integration)**

- **Client-Side Session Access:** `useSession` hook and `getSession` utility.
- **Protected Routes/Components:** Logic via layouts, component checks, or Next.js Middleware with NextAuth.js.
- **Login/Registration UI:** Client Components using NextAuth.js functions (`signIn`, `signOut`).
  Use code with caution.

5. Backend Architecture (Next.js: Route Handlers & Server Actions)
   The backend logic will be implemented within Next.js using Route Handlers and considering Server Actions for specific CUD operations.
   **5.1. API Design**

- **Route Handlers as RESTful-like Endpoints:** Use HTTP methods (GET, POST, PUT, DELETE), resource-based URLs.
- **Request/Response Structures:** Consistent JSON structures; JSDoc for clarity. Standardized success/error responses.

**5.2. Route Handlers (`app/**/route.js`)\*\*

- **Primary Use Cases:** CUD operations, complex queries, AI interactions, PDF generation, NextAuth.js backend.
- **Request Validation:** Manual or lightweight library; JSDoc for payload structures.
- **Error Handling:** Robust error handling, appropriate HTTP status codes, standardized JSON error responses.

**5.3. Server Actions**

- **Applicability:** Considered for specific CUD operations directly from forms/components if they simplify client code and align with stable features.
- **Security:** Validate user authentication/authorization. Use Next.js revalidation (`revalidatePath`, `revalidateTag`).
- **Error Handling:** Return success/failure status with messages.
- **Data Validation:** Rigorous input validation.

**5.4. AI Integration (Gemini API)**

- **Service Layer/Module (`lib/geminiService.js`):** Encapsulates Gemini API interaction logic (requests, API calls via `axios` or `fetch`, response parsing).
- **Invocation:** Called from backend Route Handlers.
- **Secure API Key Management:** Environment variables (`process.env.GEMINI_API_KEY`).
- **Error Handling:** For Gemini API calls within the service module.

**5.5. PDF Generation (Puppeteer)**

- **Service Layer/Module (`lib/pdfService.js`):** Handles PDF generation using Puppeteer (launch, render HTML, return PDF).
- **Invocation:** Called from backend Route Handlers.
- **Templating:** Dynamic HTML content for PDFs.
- **Resource Management:** Proper Puppeteer instance management.

**5.6. Business Logic**

- **Location:** Primarily in service functions/modules in `lib/` (called by Route Handlers/Server Actions), or directly in handlers for simpler logic.
- **Key Logic Areas:** Quiz CUD, automated grading, user management, class management, interactive classroom logic.
  Use code with caution.

6. Database Architecture
   **6.1. Choice of Database: PostgreSQL (Version 15+)**

- _Rationale:_ Relational integrity, scalability, feature-rich, community support, Prisma compatibility.

**6.2. ORM (Object-Relational Mapper): Prisma (Version 5+)**

- _Rationale:_ Modern ORM, schema-first, intuitive query API, migrations, Prisma Client, Prisma Studio.
- **Prisma Client Setup:** Single, reused instance from `lib/prisma.js`.
  ```javascript
  // lib/prisma.js
  import { PrismaClient } from "@prisma/client";
  let prisma;
  if (process.env.NODE_ENV === "production") {
    prisma = new PrismaClient();
  } else {
    if (!global.prisma) {
      global.prisma = new PrismaClient();
    }
    prisma = global.prisma;
  }
  export default prisma;
  ```

**6.3. Data Modeling & Schema (`schema.prisma`)**
Key Data Models:

- **`User`**: `id`, `email`, `hashedPassword`, `name`, `role` (Enum: `TEACHER`, `STUDENT`, `ADMIN`), `createdAt`, `updatedAt`, relations.
- **`Account` (NextAuth.js):** Standard model for OAuth.
- **`Session` (NextAuth.js):** Standard model (if JWT strategy is not used, but we are using JWTs, so this might be optional unless other parts of NextAuth.js require it).
- **`VerificationToken` (NextAuth.js):** Standard model.
- **`Class` (or `Group`):** `id`, `name`, `description`, `teacherId`, relations.
- **`ClassEnrollment`**: Junction: `studentId`, `classId`.
- **`Quiz`**: `id`, `title`, `description`, `creatorId`, `timeLimitMinutes`, `attemptsAllowed`, relations.
- **`Question`**: `id`, `quizId`, `text`, `type` (Enum: `MULTIPLE_CHOICE_SINGLE`, etc.), `points`, `options` (Json), `correctAnswer` (String), `orderInQuiz`.
- **`QuizAssignment`**: `id`, `quizId`, `classId`, `availableFrom`, `availableUntil`.
- **`QuizSubmission`**: `id`, `quizAssignmentId` (or `quizId`), `studentId`, `startedAt`, `submittedAt`, `score`, `answers` (Json).
- **`InteractiveClassroomSession`**: `id`, `quizId`, `teacherId`, `classId`, `sessionDate`, `correctAnswersLog` (Json).
- **Indexing Strategy:** On foreign keys and frequently queried fields.
- **Data Integrity:** Via relations, foreign keys, and non-optional fields.
  Use code with caution.

7. Security Considerations
   **7.1. Authentication & Authorization**

- **Authentication Provider:** NextAuth.js (Auth.js v5).
- **Authentication Strategies for MVP:**
  - **Credentials Provider (Email/Password):** Secure password hashing (e.g., bcrypt). Password reset/email verification deferred for MVP.
  - **Google OAuth Provider:** Integration for login/registration via Google. Standard NextAuth.js `Account` model for OAuth linking.
- **Session Management:**
  - **Strategy:** JSON Web Tokens (JWT) managed by NextAuth.js (default when no DB adapter for sessions).
  - _Storage:_ HttpOnly cookies.
  - _Security:_ JWT signed with a strong secret (`NEXTAUTH_SECRET`).
- **Authorization (Role-Based Access Control - RBAC):**
  - User roles (`ADMIN`, `TEACHER`, `STUDENT`) in `User` model, included in JWT.
  - Enforcement on backend (Route Handlers, Server Actions) and frontend (UX).
  - Access control via middleware or embedded checks.

**7.2. Input Validation**

- **Client-Side:** React Hook Form for UX.
- **Server-Side (Critical):** Rigorous validation in Route Handlers/Server Actions for all incoming data (manual or lightweight library).

**7.3. Output Encoding (Mitigating Cross-Site Scripting - XSS)**

- Rely on React's auto-escaping. Avoid `dangerouslySetInnerHTML`.
- Correct JSON serialization for API responses.

**7.4. API Security (Protecting Route Handlers & Server Actions)**

- Authentication/Authorization checks for all sensitive operations.
- Rate Limiting (Post-MVP consideration).

**7.5. Secret Management**

- Environment variables (`.env.local`, Vercel UI) for all secrets. Not committed to Git. Server-side access only.

**7.6. CSRF (Cross-Site Request Forgery) Protection**

- Leverage NextAuth.js built-in CSRF protection.

**7.7. Error Handling & Information Exposure**

- Generic error messages to users. Detailed errors logged server-side only.

**7.8. Dependency Management**

- Regular updates, `npm audit` for vulnerabilities.

**7.9. HTTPS**

- Application served over HTTPS in production (handled by Vercel).

**7.10. Database Security**

- Principle of Least Privilege for database user.
- Prisma ORM mitigates SQL injection; avoid raw SQL with user input.
  Use code with caution.

8. Deployment Architecture (Initial Plan for MVP)
   **8.1. Target Platform(s)**

- **Next.js Application (Frontend & Backend Logic): Vercel**
  - _Rationale:_ Native Next.js support, serverless functions, global CDN, CI/CD, free tier, environment variable management, custom domains & SSL.
- **Database: Neon (Managed Serverless PostgreSQL)**
  - _Rationale:_ Serverless architecture (scales to zero, auto-scales), modern developer experience (branching), generous free tier, Prisma compatibility, efficient connection handling, cost-effective.

**8.2. Build Process**

- `next build` (handled by Vercel).
- `prisma generate` (as part of build/post-install).

**8.3. Environment Variables Management**

- Vercel Dashboard for production/preview/development environments.
- `.env.local` for local development.

**8.4. CI/CD (Continuous Integration / Continuous Deployment)**

- Vercel Git Integration (GitHub, GitLab, etc.) for automated builds and deployments on pushes to main/preview branches.

**8.5. Domain & DNS**

- Custom domain configured to point to Vercel.
- Vercel handles SSL.
  Use code with caution.

9. Performance Considerations
   **9.1. Frontend Performance**

- **Next.js App Router Optimizations:** Automatic code splitting, Server Components, optimized Client Components, `loading.js` UI.
- **Efficient State Management:** Zustand (small bundle), React Query (caching, preventing re-fetches).
- **Bundle Size Awareness:** Mindful of external package sizes.
- **Efficient Rendering:** `React.memo` where appropriate, minimize expensive computations.

**9.2. Backend Performance (Next.js Route Handlers & Server Actions)**

- **Efficient Database Queries (Prisma):** Select specific fields, indexing, avoid N+1, Prisma connection pooling.
- **AI API (Gemini) Interaction:** Asynchronous operations, mindful of payload size, timeout handling. Caching AI responses (Post-MVP).
- **PDF Generation (Puppeteer):** Asynchronous, optimize HTML, manage Puppeteer instances.
- **Serverless Function Cold Starts:** Aware of potential, default Vercel behavior acceptable for MVP.

**9.3. Caching Strategies**

- **Next.js Data Cache:** For `fetch` in Server Components.
- **React Query Caching:** Client-side server state.
- **Browser Caching:** Vercel CDN for static assets.
- **Database Query Caching (Post-MVP):** Dedicated cache like Redis if needed.

**9.4. Monitoring & Optimization (Post-MVP Planning)**

- Vercel Analytics. Integrate logging/monitoring service post-MVP. Review slow queries.
  Use code with caution.

10. Error Handling & Logging
    **10.1. Frontend Error Handling**

- **Next.js Error Boundaries (`error.js`):** Catch rendering/data fetching errors, display user-friendly message, provide retry.
- **React Query Error States:** Handle API request failures in Client Components.
- **Client-Side Form Validation Errors:** Displayed by React Hook Form.
- **Goal:** Avoid blank screens/crashes.

**10.2. Backend Error Handling (Next.js Route Handlers & Server Actions)**

- **Standardized Error Responses:** JSON errors with appropriate HTTP status codes.
- **Error Handling in Business Logic:** Try-catch in service functions. Custom error classes.
- **Prisma Error Handling:** Catch specific Prisma errors, map to user-friendly messages.
- **Server Action Error Handling:** Return success/failure status.

**10.3. Logging Strategy (MVP)**

- **Purpose:** Debugging during development, identifying critical production errors.
- **Client-Side Logging:** `console.log()` for development (remove/wrap for production). Errors from boundaries logged to console.
- **Server-Side Logging (Vercel Functions):** `console.log()`, `console.warn()`, `console.error()`. Vercel captures this. Log key events, errors, context.
- **Log Levels (Conceptual):** INFO, WARN, ERROR.
- **What NOT to Log:** Sensitive information (passwords, raw API keys).
- **Log Retention & Analysis (Vercel):** Use Vercel dashboard for MVP.
- **Post-MVP:** Consider dedicated logging service.
  Use code with caution.

11. Scalability (Future Considerations)
    **11.1. Frontend Scalability (Next.js on Vercel)**

- Vercel's auto-scaling CDN. Component-based architecture. Automatic code splitting.

**11.2. Backend Scalability (Next.js Serverless Functions on Vercel)**

- Serverless function auto-scaling. Stateless design.
- **Potential Bottlenecks & Evolution (Post-MVP):** Long-running tasks (offload to background workers), monolith complexity (potential move to distributed services long-term).

**11.3. Database Scalability (Neon - Serverless PostgreSQL)**

- Neon's serverless scaling (compute/storage). Read replicas (if needed). Connection management. Vertical scaling.

**11.4. AI Integration (Gemini API)**

- External service scales independently. Mindful of rate limits/quotas. Cost management.

**11.5. Caching**

- Leverage existing Next.js, React Query, Vercel CDN caches. Dedicated cache (Redis) post-MVP if needed.

**11.6. Asynchronous Processing (Future Enhancement)**

- Message queues for long-running tasks if features requiring them are added.
  Use code with caution.

12. Development Practices & Conventions (MVP Focus)
    **12.1. Naming Conventions**

- **JS Variables/Functions:** `camelCase`.
- **React Components:** `kebab-case`.
- **Constants:** `UPPER_SNAKE_CASE`.
- **Files (Non-Component):** `camelCase` or `kebab-case`. Next.js specific files lowercase.

**12.2. Code Commenting & Documentation**

- **JSDoc for Functions:** Purpose, `@param {type} name`, `@returns {type}`. Critical for JS-only.
- **Complex Logic:** Inline comments (`//`).
- **TODOs:** `// TODO:`.
- **Prisma Schema Comments:** `///`.

**12.3. Code Formatting & Linting**

- **Prettier:** Auto-formatting.
- **ESLint:** `eslint-config-next` base + plugins (React, Hooks, JSX-A11Y). Address errors/warnings promptly.

**12.4. Git Workflow**

- **Repository:** Git (GitHub, etc.).
- **Main Branch:** `main` (production).
- **Feature Branches:** `feat/feature-name`, `fix/bug-name`.
- **Pull Requests (PRs):** Self-review for solo dev. Vercel preview deployments.
- **Commit Messages:** Conventional Commits (e.g., `feat: ...`, `fix: ...`).
- **Regular Commits:** Small, logical units.

**12.5. Manual Testing Strategy for MVP**

- **Test Plan/Checklist:** Based on PRD for key user flows.
- **Cross-Browser Testing (Basic):** Chrome, Firefox, mobile browser for student view.
- **Edge Cases:** Test invalid inputs, unexpected actions.
- **Pre-Deployment Testing:** On Vercel preview deployments before merging to `main`.

**12.6. Environment Management**

- Separate env vars for development (`.env.local`), preview, production (Vercel UI).

**12.7. Dependency Management**

- Review new dependencies. Periodically update and audit (`npm audit`).
  Use code with caution.

13. Risks & Mitigation (Technical)
    **13.1. Risk: Runtime Errors and Reduced Maintainability due to Lack of TypeScript**

- _Likelihood:_ High. _Impact:_ Medium to High.
- _Mitigation:_ Diligent JSDoc, strict ESLint, thorough manual testing, clear modular design, code reviews (self), consider TypeScript post-MVP.

**13.2. Risk: Regressions and Higher Bug Rate due to Lack of Automated Tests**

- _Likelihood:_ High. _Impact:_ Medium to High.
- _Mitigation:_ Comprehensive manual test plan, Vercel preview deployments for testing, careful refactoring, user feedback channels, prioritize test implementation post-MVP.

**13.3. Risk: Performance Bottlenecks with AI/PDF Generation in Serverless Functions**

- _Likelihood:_ Medium. _Impact:_ Medium.
- _Mitigation:_ Asynchronous handling, user feedback (loading states), optimize payloads/templates, monitor performance, increase function timeout (if possible/needed), consider background jobs post-MVP.

**13.4. Risk: Security Vulnerabilities**

- _Likelihood:_ Medium. _Impact:_ High.
- _Mitigation:_ Adherence to Security Best Practices (Section 7), dependency management, HTTPS, Principle of Least Privilege, regular security reviews (conceptual).

**13.5. Risk: Scalability Limits of MVP Architecture (Long-Term)**

- _Likelihood:_ Low (MVP/Near-term), Medium (Long-term if hyper-growth). _Impact:_ Medium.
- _Mitigation:_ Modular design within monolith, monitor performance/complexity, iterative refactoring if needed.

**13.6. Risk: Vendor Lock-in (e.g., Vercel, Neon)**

- _Likelihood:_ Low. _Impact:_ Low to Medium.
- _Mitigation:_ Use of standard technologies (Next.js, PostgreSQL), consider containerization post-MVP, prioritize productivity gains for MVP.
