**MVP Development Implementation Phases: AI-Powered QuizMaker (Revised Part 1 with "Pages Developed" & Stricter Phase Coherence)**

**Guiding Principle:** Initial phases focus on building the static UI with mock data, applying frontend best practices from the start, to allow for early visual review and confirmation. Each phase clearly lists the pages developed. Subsequent phases will implement backend logic and integrate it with the frontend components. "Polish and refinement" of UI/UX will be handled separately by the developer.

**Part 1: Static Frontend UI Development (with Mock Data)**

**Phase 1.A: Core Application Shell, Static Public Landing Page & Authentication UI**

- **1. Goal of the Phase:**
  Establish the root application structure, create a static public landing page, and develop static UI screens for user authentication flows using mock data, adhering to component best practices.
- **2. High-Level Sequential Tasks/Instructions:**
  1.  **Project Setup:** Initialize Next.js project; configure Tailwind CSS, ESLint, Prettier.
  2.  **Root Layout:** Create the root application layout (`app/layout.jsx`) using semantic HTML. This wraps all pages. (Best Practice: Semantic HTML, Next.js Layouts)
  3.  **Static Public Landing Page:**
      - Design and develop the static UI for the public landing page (`app/page.jsx`).
      - Include sections for app introduction, features, benefits, CTAs ("Sign Up," "Login"). Use placeholder content/images.
      - (Best Practice: Component-Based for sections, Responsive Design, Accessibility Basics)
  4.  **Auth Layout:** Create a specific layout for auth pages (`app/(auth)/layout.jsx`) (e.g., centered content), inheriting from the root layout. (Best Practice: Next.js Layouts)
  5.  **Component Design (Auth Forms):** Design `LoginFormComponent.jsx` and `RegistrationFormComponent.jsx` (in `components/features/auth/`) as presentational components. Use JSDoc for prop definitions. (Best Practice: Component-Based, Props, JSDoc)
  6.  **Static Auth Pages:** Develop static UI pages using the auth form components and mock data:
      - Login Page (`app/(auth)/login/page.jsx`)
      - Registration Page (`app/(auth)/register/page.jsx`)
        (Best Practice: Realistic Mock Data Structures)
  7.  **Shadcn/UI Integration:** Use basic Shadcn/UI components (Button, Input, Label) within the landing page and auth forms. (Best Practice: Consistent Use of Shadcn/UI)
- **3. Pages Developed in this Phase:**
  - `app/page.jsx` (Public Landing Page)
  - `app/(auth)/login/page.jsx` (Login Page)
  - `app/(auth)/register/page.jsx` (Registration Page)
- **4. Check of the Phase Implementation (Visual & Structural):**
  - Static public landing page, login, and registration pages are visually complete with mock elements.
  - JSDoc for form component props.
  - Root and auth layouts are correctly structured.
  - ESLint/Prettier functional.

**Phase 1.B: Static Teacher Dashboard Shell & Core Quiz Management UI**

- **1. Goal of the Phase:**
  Establish the teacher dashboard shell and build static UI for core teacher quiz management (listing, creating, editing quizzes and questions) using mock data.
- **2. High-Level Sequential Tasks/Instructions:**
  1.  **Teacher Dashboard Layout:** Create the static layout for the teacher dashboard (`app/(dashboard)/layout.jsx`), including placeholders for `Navbar.jsx` and `Sidebar.jsx` (from `components/layout/`). This layout inherits from the root layout. (Best Practice: Next.js Layouts, Component-Based)
  2.  **Dashboard Overview Page:** Develop the static UI for the main Teacher Dashboard Overview (`app/(dashboard)/dashboard/page.jsx`) with placeholder content sections.
  3.  **Quiz List UI:**
      - Design `QuizCard.jsx` (in `components/features/quiz/`) (JSDoc for props).
      - Create the static "List Quizzes" page (`app/(dashboard)/dashboard/quizzes/page.jsx`), using `QuizCard.jsx` with mock quizzes. (Best Practice: Component Reusability, Realistic Mock Data)
  4.  **Quiz Creation/Editing Forms UI:**
      - Design `QuizForm.jsx` (in `components/features/quiz/`) for quiz details.
      - Develop static UI pages for "Create Quiz" (`.../quizzes/create/page.jsx`) and "Edit Quiz Details" (`.../quizzes/[quizId]/edit/details/page.jsx`) using `QuizForm.jsx` with mock data.
  5.  **Question Management UI:**
      - Design `QuestionForm.jsx` and `QuestionDisplay.jsx` (in `components/features/quiz/`) for various auto-gradable question types with mock content. (JSDoc for props)
      - Develop the static "Edit Quiz Questions" page (`.../quizzes/[quizId]/edit/questions/page.jsx`) using these components.
  6.  **AI Generation UI (Page Shell):** Develop the static UI for the "AI Quiz Generation" page (`.../quizzes/generate-ai/page.jsx`) with PDF upload (visual placeholder) and topic input elements, and a mock review/edit step.
  7.  **Styling & Responsiveness:** Apply Tailwind CSS; ensure basic responsive behavior for these pages. (Best Practice: Utility-First, Responsive Design)
- **3. Pages Developed in this Phase:**
  - `app/(dashboard)/dashboard/page.jsx` (Teacher Dashboard Overview)
  - `app/(dashboard)/dashboard/quizzes/page.jsx` (List Quizzes)
  - `app/(dashboard)/dashboard/quizzes/create/page.jsx` (Create Quiz)
  - `app/(dashboard)/dashboard/quizzes/[quizId]/edit/details/page.jsx` (Edit Quiz Details)
  - `app/(dashboard)/dashboard/quizzes/[quizId]/edit/questions/page.jsx` (Edit Quiz Questions)
  - `app/(dashboard)/dashboard/quizzes/generate-ai/page.jsx` (AI Quiz Generation Options)
- **4. Check of the Phase Implementation (Visual & Structural):**
  - Teacher dashboard layout is in place.
  - Listed quiz management pages are visually rendered with mock data.
  - Forms are displayed with mock data; component props documented.
  - Basic responsiveness is visible.

**Phase 1.C: Static Teacher Dashboard UI - Class Management & Quiz Assignment**

- **1. Goal of the Phase:**
  Build the static UI for teacher class management and quiz assignment functionalities within the teacher dashboard, using mock data.
- **2. High-Level Sequential Tasks/Instructions:**
  1.  **Class List UI:** Design `ClassCard.jsx` (in `components/features/class/`) and use it on the static "List Classes" page (`app/(dashboard)/dashboard/classes/page.jsx`) with mock class data. (JSDoc for props)
  2.  **Class Forms UI:** Design `ClassForm.jsx` (in `components/features/class/`) and use it on static "Create Class" (`.../classes/create/page.jsx`) and "Edit Class" (`.../classes/[classId]/edit/page.jsx`) pages with mock data.
  3.  **Enrollment UI:** Develop static UI for "Enroll Students" page (`app/(dashboard)/dashboard/classes/[classId]/enroll/page.jsx`), showing mock student lists.
  4.  **Assignment UI:** Create static UI for "Assign Quiz" page (`app/(dashboard)/dashboard/quizzes/[quizId]/assign/page.jsx`), allowing selection of mock classes and setting mock availability dates.
  5.  **Accessibility Basics:** Ensure forms use appropriate labels and interactive elements have clear visual cues. (Best Practice: Accessibility Basics)
- **3. Pages Developed in this Phase:**
  - `app/(dashboard)/dashboard/classes/page.jsx` (List Classes)
  - `app/(dashboard)/dashboard/classes/create/page.jsx` (Create Class)
  - `app/(dashboard)/dashboard/classes/[classId]/edit/page.jsx` (Edit Class)
  - `app/(dashboard)/dashboard/classes/[classId]/enroll/page.jsx` (Enroll Students)
  - `app/(dashboard)/dashboard/quizzes/[quizId]/assign/page.jsx` (Assign Quiz)
- **4. Check of the Phase Implementation (Visual & Structural):**
  - Listed class management and assignment pages are visually rendered with mock data.
  - Forms are displayed; basic accessibility in form structures.

**Phase 1.D: Static Student Dashboard Shell & Quiz Taking UI**

- **1. Goal of the Phase:**
  Establish the student dashboard shell and build the static UI for the student quiz listing and online quiz-taking experience, using mock data.
- **2. High-Level Sequential Tasks/Instructions:**
  1.  **Student Dashboard Layout:** Create the static layout for the student dashboard (`app/(student)/layout.jsx`), including placeholders for any student-specific navigation. Inherits from root layout. (Best Practice: Next.js Layouts)
  2.  **Student Dashboard UI:** Develop static UI for Student Dashboard Overview (`app/(student)/dashboard/page.jsx`) and "List Assigned Quizzes" page (`app/(student)/dashboard/quizzes/page.jsx`), showing mock quiz assignments.
  3.  **Quiz Taking UI:**
      - Design `QuestionDisplayStudentView.jsx` (in `components/features/quiz/`).
      - Create static "Take Quiz" page (`app/(student)/dashboard/quizzes/[quizId]/take/page.jsx`), displaying mock questions, timer, navigation.
  4.  **Quiz Result UI:** Develop static UI for "Student Quiz Result" page (`app/(student)/dashboard/quizzes/[quizId]/result/page.jsx`), showing a mock score.
  5.  **Responsive Design:** Ensure all student-facing UIs are responsive. (Best Practice: Responsive Design)
- **3. Pages Developed in this Phase:**
  - `app/(student)/dashboard/page.jsx` (Student Dashboard Overview)
  - `app/(student)/dashboard/quizzes/page.jsx` (List Assigned Quizzes - Student)
  - `app/(student)/dashboard/quizzes/[quizId]/take/page.jsx` (Take Quiz - Student)
  - `app/(student)/dashboard/quizzes/[quizId]/result/page.jsx` (Quiz Result - Student)
- **4. Check of the Phase Implementation (Visual & Structural):**
  - Student dashboard layout is in place.
  - Student-facing pages are visually rendered with mock data and are responsive.

**Phase 1.E: Static UI for Specialized Delivery, Reporting & Question Bank (Teacher)**

- **1. Goal of the Phase:**
  Build static UI for teacher's specialized quiz delivery options (print, interactive), reporting views, and personal question bank, using mock data.
- **2. High-Level Sequential Tasks/Instructions:**
  1.  **Printable Quiz Options UI:** On relevant teacher quiz pages (e.g., `app/(dashboard)/dashboard/quizzes/[quizId]/page.jsx`), add static UI elements (buttons, modals with mock options) related to "Print Quiz".
  2.  **Interactive Classroom UI:** Create static "Interactive Classroom Display" page (`app/(dashboard)/dashboard/quizzes/[quizId]/take-interactive/page.jsx`), showing a large-format question, mock navigation, and student selection placeholders.
  3.  **Teacher Reports UI:** Develop static "Quiz Results Page (Teacher)" (`app/(dashboard)/dashboard/quizzes/[quizId]/results/page.jsx`), using mock data for charts/tables. (Best Practice: Representing Different States)
  4.  **Question Bank UI:** Create static "Personal Question Bank" page (`app/(dashboard)/dashboard/question-bank/page.jsx`), displaying mock questions and management options.
- **3. Pages Developed in this Phase (or significant UI additions to existing pages):**
  - `app/(dashboard)/dashboard/quizzes/[quizId]/take-interactive/page.jsx` (Interactive Classroom Mode)
  - `app/(dashboard)/dashboard/quizzes/[quizId]/results/page.jsx` (Teacher Quiz Results)
  - `app/(dashboard)/dashboard/question-bank/page.jsx` (Question Bank)
  - (UI elements for "Print Quiz" added to relevant existing quiz management pages)
- **4. Check of the Phase Implementation (Visual & Structural):**
  - Static UI for print options, interactive classroom, teacher reports, and question bank is displayed with mock data.

---
