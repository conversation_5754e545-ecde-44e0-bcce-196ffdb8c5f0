@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Custom Quiz App Color Palette */
    --background: 210 20% 98%; /* #f8fafc - Light, clean background */
    --foreground: 210 24% 8%; /* #0d141c - Primary text, dark high contrast */
    --card: 0 0% 100%; /* Pure white for cards */
    --card-foreground: 210 24% 8%; /* #0d141c - Primary text on cards */
    --popover: 0 0% 100%; /* Pure white for popovers */
    --popover-foreground: 210 24% 8%; /* #0d141c - Primary text on popovers */
    --primary: 213 94% 68%; /* #248bf3 - Primary action blue */
    --primary-foreground: 0 0% 98%; /* Light text on primary */
    --secondary: 210 20% 98%; /* #f8fafc - Secondary background */
    --secondary-foreground: 210 24% 8%; /* #0d141c - Text on secondary */
    --muted: 210 20% 95%; /* Slightly darker than background for muted areas */
    --muted-foreground: 210 16% 46%; /* #49739c - Secondary text, muted blue */
    --accent: 213 94% 68%; /* #248bf3 - Accent color same as primary */
    --accent-foreground: 0 0% 98%; /* Light text on accent */
    --destructive: 0 84.2% 60.2%; /* Red for destructive actions */
    --destructive-foreground: 0 0% 98%; /* Light text on destructive */
    --border: 210 20% 90%; /* Light border color */
    --input: 210 20% 95%; /* Input background */
    --ring: 213 94% 68%; /* #248bf3 - Focus ring color */

    /* Quiz-specific color tokens */
    --quiz-primary: 213 94% 68%; /* #248bf3 - Main quiz action color */
    --quiz-secondary: 210 16% 46%; /* #49739c - Secondary quiz elements */
    --quiz-success: 142 76% 36%; /* Green for correct answers */
    --quiz-warning: 38 92% 50%; /* Orange for warnings */
    --quiz-error: 0 84% 60%; /* Red for incorrect answers */

    /* Chart colors for quiz analytics */
    --chart-1: 213 94% 68%; /* Primary blue */
    --chart-2: 142 76% 36%; /* Success green */
    --chart-3: 38 92% 50%; /* Warning orange */
    --chart-4: 0 84% 60%; /* Error red */
    --chart-5: 270 95% 75%; /* Purple accent */

    --radius: 0.5rem;
  }
  .dark {
    /* Dark mode color palette */
    --background: 210 24% 8%; /* #0d141c - Dark background */
    --foreground: 210 20% 98%; /* #f8fafc - Light text */
    --card: 210 20% 12%; /* Slightly lighter than background for cards */
    --card-foreground: 210 20% 98%; /* Light text on cards */
    --popover: 210 20% 12%; /* Card color for popovers */
    --popover-foreground: 210 20% 98%; /* Light text on popovers */
    --primary: 213 94% 68%; /* #248bf3 - Keep primary blue */
    --primary-foreground: 210 24% 8%; /* Dark text on primary */
    --secondary: 210 20% 15%; /* Dark secondary background */
    --secondary-foreground: 210 20% 98%; /* Light text on secondary */
    --muted: 210 20% 15%; /* Muted background */
    --muted-foreground: 210 16% 65%; /* Lighter version of secondary text */
    --accent: 213 94% 68%; /* #248bf3 - Keep accent blue */
    --accent-foreground: 210 24% 8%; /* Dark text on accent */
    --destructive: 0 84% 60%; /* Red for destructive actions */
    --destructive-foreground: 0 0% 98%; /* Light text on destructive */
    --border: 210 20% 20%; /* Dark border */
    --input: 210 20% 15%; /* Dark input background */
    --ring: 213 94% 68%; /* #248bf3 - Focus ring */

    /* Quiz-specific dark mode colors */
    --quiz-primary: 213 94% 68%; /* #248bf3 - Main quiz action color */
    --quiz-secondary: 210 16% 65%; /* Lighter secondary for dark mode */
    --quiz-success: 142 76% 45%; /* Brighter green for dark mode */
    --quiz-warning: 38 92% 60%; /* Brighter orange for dark mode */
    --quiz-error: 0 84% 70%; /* Brighter red for dark mode */

    /* Chart colors for dark mode */
    --chart-1: 213 94% 68%; /* Primary blue */
    --chart-2: 142 76% 45%; /* Success green */
    --chart-3: 38 92% 60%; /* Warning orange */
    --chart-4: 0 84% 70%; /* Error red */
    --chart-5: 270 95% 80%; /* Brighter purple accent */
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-lexend;
    font-feature-settings:
      "rlig" 1,
      "calt" 1;
  }

  /* Typography improvements for quiz content */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-lexend;
    font-feature-settings:
      "rlig" 1,
      "calt" 1;
  }

  /* Smooth transitions for interactive elements */
  button,
  input,
  textarea,
  select {
    @apply transition-colors duration-200;
  }

  /* Focus styles for accessibility */
  :focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }

  /* Quiz-specific utility classes */
  .quiz-primary {
    color: hsl(var(--quiz-primary));
  }

  .quiz-secondary {
    color: hsl(var(--quiz-secondary));
  }

  .quiz-success {
    color: hsl(var(--quiz-success));
  }

  .quiz-warning {
    color: hsl(var(--quiz-warning));
  }

  .quiz-error {
    color: hsl(var(--quiz-error));
  }
}
