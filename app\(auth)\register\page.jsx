"use client";

import { useState } from "react";
import RegistrationFormComponent from "@/components/features/auth/RegistrationFormComponent";

/**
 * Mock existing users for email validation
 */
const existingEmails = [
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>"
];

export default function RegisterPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  /**
   * Handle registration form submission with mock user creation
   * @param {Object} formData - Registration form data
   * @param {string} formData.firstName - User's first name
   * @param {string} formData.lastName - User's last name
   * @param {string} formData.email - User's email
   * @param {string} formData.password - User's password
   * @param {string} formData.confirmPassword - Password confirmation
   * @param {string} formData.role - User's role (teacher/student/admin)
   * @param {string} formData.institution - User's institution (optional)
   */
  const handleRegistration = async (formData) => {
    setIsLoading(true);
    setError("");

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Mock validation: Check if email already exists
      if (existingEmails.includes(formData.email.toLowerCase())) {
        setError("An account with this email already exists. Please use a different email or try logging in.");
        return;
      }

      // Mock validation: Password strength
      if (formData.password.length < 8) {
        setError("Password must be at least 8 characters long.");
        return;
      }

      // Mock validation: Password confirmation
      if (formData.password !== formData.confirmPassword) {
        setError("Passwords do not match.");
        return;
      }

      // Simulate successful registration
      const newUser = {
        id: Date.now(),
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        role: formData.role,
        institution: formData.institution,
        createdAt: new Date().toISOString(),
        isVerified: false
      };

      console.log("Registration successful:", newUser);

      // In a real app, you would:
      // 1. Send verification email
      // 2. Store user data in database
      // 3. Redirect to verification page or dashboard
      // 4. Update global auth state

      alert(`Welcome to AI QuizMaker, ${formData.firstName}! Please check your email to verify your account.`);

      // Mock redirect to verification page
      window.location.href = "/verify-email";

    } catch (err) {
      setError("An error occurred during registration. Please try again.");
      console.error("Registration error:", err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="animate-fade-in">
      <RegistrationFormComponent
        onSubmit={handleRegistration}
        isLoading={isLoading}
        error={error}
        initialData={{
          firstName: "",
          lastName: "",
          email: "",
          password: "",
          confirmPassword: "",
          role: "teacher",
          institution: ""
        }}
      />

      {/* Registration Benefits */}
      <div className="mt-6 p-4 bg-quiz-primary/5 rounded-lg border border-quiz-primary/20">
        <h3 className="text-sm font-medium text-foreground mb-2">What you'll get:</h3>
        <ul className="space-y-1 text-xs text-muted-foreground">
          <li className="flex items-center">
            <svg className="w-3 h-3 text-quiz-success mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
            14-day free trial with full access
          </li>
          <li className="flex items-center">
            <svg className="w-3 h-3 text-quiz-success mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
            AI-powered quiz generation
          </li>
          <li className="flex items-center">
            <svg className="w-3 h-3 text-quiz-success mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
            Unlimited quiz creation and delivery
          </li>
          <li className="flex items-center">
            <svg className="w-3 h-3 text-quiz-success mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
            Detailed analytics and reporting
          </li>
          <li className="flex items-center">
            <svg className="w-3 h-3 text-quiz-success mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
            Priority customer support
          </li>
        </ul>
      </div>
    </div>
  );
}
