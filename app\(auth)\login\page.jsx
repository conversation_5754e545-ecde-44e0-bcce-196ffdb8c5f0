"use client";

import { useState } from "react";
import LoginFormComponent from "@/components/features/auth/LoginFormComponent";

/**
 * Mock user data for demonstration
 */
const mockUsers = [
  {
    email: "<EMAIL>",
    password: "password123",
    role: "teacher",
    name: "<PERSON>"
  },
  {
    email: "<EMAIL>", 
    password: "password123",
    role: "student",
    name: "<PERSON>"
  }
];

export default function LoginPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  /**
   * Handle login form submission with mock authentication
   * @param {Object} formData - Login form data
   * @param {string} formData.email - User email
   * @param {string} formData.password - User password
   */
  const handleLogin = async (formData) => {
    setIsLoading(true);
    setError("");

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Mock authentication logic
      const user = mockUsers.find(
        u => u.email === formData.email && u.password === formData.password
      );

      if (user) {
        // Simulate successful login
        console.log("Login successful:", user);
        
        // In a real app, you would:
        // 1. Store authentication token
        // 2. Redirect to dashboard
        // 3. Update global auth state
        
        alert(`Welcome back, ${user.name}! Redirecting to dashboard...`);
        
        // Mock redirect based on role
        if (user.role === "teacher") {
          window.location.href = "/dashboard";
        } else {
          window.location.href = "/student/dashboard";
        }
      } else {
        setError("Invalid email or password. Please try again.");
      }
    } catch (err) {
      setError("An error occurred during login. Please try again.");
      console.error("Login error:", err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="animate-fade-in">
      <LoginFormComponent
        onSubmit={handleLogin}
        isLoading={isLoading}
        error={error}
        initialData={{
          email: "",
          password: ""
        }}
      />
      
      {/* Demo Credentials Helper */}
      <div className="mt-6 p-4 bg-muted/50 rounded-lg border border-border/50">
        <h3 className="text-sm font-medium text-foreground mb-2">Demo Credentials</h3>
        <div className="space-y-2 text-xs text-muted-foreground">
          <div>
            <strong>Teacher:</strong> <EMAIL> / password123
          </div>
          <div>
            <strong>Student:</strong> <EMAIL> / password123
          </div>
        </div>
      </div>
    </div>
  );
}
