Integrating the latest version of TanStack Query (v5) into your Next.js App Router project (without TypeScript) involves a few key setup steps and an understanding of how it interacts with Server and Client Components. Here's a comprehensive guide:

**Key Changes in TanStack Query v5:**

Before diving into the setup, it's good to be aware of some significant changes from v4 if you've used it extensively. If you're new or used much older versions, these might just seem like current practice:

- **Simplified Hook Signatures:** Hooks like `useQuery` and `useMutation` now primarily accept a single options object (e.g., `useQuery({ queryKey, queryFn, ...options })`).
- **Callbacks Removed from `useQuery`:** Callbacks like `onSuccess`, `onError`, and `onSettled` have been removed from `useQuery` options. Side effects should now typically be handled with `useEffect` in your component or via the `meta` property on mutations.
- **`cacheTime` Renamed to `gcTime`:** The option to control how long inactive query data is kept in the cache is now `gcTime` (garbage collection time).
- **`keepPreviousData` Replaced:** The `keepPreviousData` option is replaced by `placeholderData`. To get similar behavior, you can use `placeholderData: (previousData) => previousData`.
- **Infinite Queries Require `initialPageParam`:** You must now define an `initialPageParam` for `useInfiniteQuery`.
- **`queryClient.removeQueries`:** The `removeQueries` method has been removed from `useQuery` but is still available on the `queryClient`.
- **Devtools Import Change:** The devtools are now imported from `@tanstack/react-query-devtools`.

**Migration and Integration Steps:**

**1. Installation:**

Install the necessary packages:

```bash
npm install @tanstack/react-query @tanstack/react-query-devtools
# or
yarn add @tanstack/react-query @tanstack/react-query-devtools
# or
pnpm add @tanstack/react-query @tanstack/react-query-devtools
```

**2. Create a Query Client Instance:**

It's a best practice to create a single instance of `QueryClient` that can be shared across your application. You can customize default options here.

Create a file, for example, `lib/queryClient.js` (or `utils/queryClient.js`):

```javascript
// lib/queryClient.js
import { QueryClient } from "@tanstack/react-query";

// Option 1: Create a new QueryClient instance on each request for server components,
// and a singleton for client-side. This is important to prevent data leakage between users
// when using React Server Components or prefetching on the server.
// More on this in the "Server Components and Hydration" section.

// For now, let's define a function to get the client.
// We'll refine this later.
// const queryClient = new QueryClient({
//   defaultOptions: {
//     queries: {
//       staleTime: 1000 * 60 * 5, // 5 minutes
//       gcTime: 1000 * 60 * 30, // 30 minutes
//     },
//   },
// });

// export default queryClient;

// For Next.js App Router and to avoid sharing data between requests on the server:
// It's often recommended to create a new QueryClient instance per request when server-side rendering
// or fetching in Server Components, or to manage it carefully.
// A common pattern is to have a function that returns a new client or a cached client.

// Let's start with a simpler approach for client-side focus first,
// then enhance for RSC. For client-side providers, a singleton is fine.
let browserQueryClient;

function getQueryClient() {
  if (typeof window === "undefined") {
    // Server: always make a new query client
    return makeQueryClient();
  } else {
    // Browser: make a new query client if we don't already have one
    // This is very important so we don't re-make a new client if React
    // suspends during the initial render. This may not be needed if we
    // have a suspense boundary BELOW the creation of the query client
    if (!browserQueryClient) browserQueryClient = makeQueryClient();
    return browserQueryClient;
  }
}

function makeQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // With SSR, we usually want to set some default staleTime
        // above 0 to avoid refetching immediately on the client
        staleTime: 60 * 1000, // 1 minute
        gcTime: 1000 * 60 * 30, // 30 minutes
      },
    },
  });
}

export default getQueryClient;
```

The official TanStack Query documentation for Next.js App Router suggests creating the `QueryClient` instance in a way that ensures a new client is used for each server request to prevent sharing data between users, while reusing a client instance on the browser.

**3. Create a Providers Component (Client Component):**

`QueryClientProvider` uses React Context and thus must be part of a Client Component.

Create `app/components/Providers.jsx` (or a similar location):

```javascript
// app/components/Providers.jsx
"use client";

import { useState } from "react";
import { QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import getQueryClient from "@/lib/queryClient"; // Adjust path as needed

export default function Providers({ children }) {
  // Use a state to ensure the QueryClient is only created once per client session.
  // NOTE: For Next.js App Router with Server Components and prefetching,
  // the `getQueryClient` function handles server/client differences.
  const [queryClient] = useState(() => getQueryClient());

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
}
```

**4. Use the Providers Component in Your Root Layout:**

Wrap your application's children with the `Providers` component in your root `layout.js`.

```javascript
// app/layout.js
import Providers from "./components/Providers"; // Adjust path as needed
import "./globals.css"; // Or your global stylesheet

export const metadata = {
  title: "My Next App with TanStack Query",
  description: "Generated by create next app",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body>
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
```

**5. Usage in Client Components:**

Now you can use TanStack Query hooks like `useQuery`, `useMutation`, etc., in any Client Component (`"use client"`).

```javascript
// app/my-data-page/page.jsx
"use client";

import { useQuery } from "@tanstack/react-query";

async function fetchPosts() {
  const res = await fetch("https://jsonplaceholder.typicode.com/posts");
  if (!res.ok) {
    throw new Error("Network response was not ok");
  }
  return res.json();
}

export default function MyDataPage() {
  const { data, error, isLoading, isFetching } = useQuery({
    queryKey: ["posts"],
    queryFn: fetchPosts,
    // staleTime: 5 * 60 * 1000, // 5 minutes, can also be set globally
  });

  if (isLoading) return <p>Loading...</p>;
  if (error) return <p>An error occurred: {error.message}</p>;

  return (
    <div>
      <h1>Posts</h1>
      {isFetching && <p>Updating...</p>}
      <ul>
        {data?.slice(0, 10).map((post) => (
          <li key={post.id}>{post.title}</li>
        ))}
      </ul>
    </div>
  );
}
```

**6. Usage with Server Components and Hydration:**

This is where TanStack Query becomes powerful with the App Router. You can fetch data in Server Components and pass it to Client Components for hydration. This ensures data is available on initial render and then managed by TanStack Query on the client.

- **Get or create a QueryClient on the server.**
- **Prefetch data using `queryClient.prefetchQuery()`.**
- **Dehydrate the `queryClient` and pass the dehydrated state to a Client Component.**
- **Hydrate the `queryClient` in the Client Component using `HydrationBoundary`.**

**Modified `getQueryClient` for Server/Client Distinction:**

The `getQueryClient` function from step 2 is designed to handle this. It creates a new client on the server for each request and a singleton on the client.

**Example: Fetching in a Server Component and Hydrating in a Client Component:**

```javascript
// app/server-fetched-posts/page.jsx (Server Component)
import getQueryClient from "@/lib/queryClient"; // Adjust path
import { dehydrate, HydrationBoundary } from "@tanstack/react-query";
import PostListClient from "./PostListClient"; // Client component to display posts

async function getPosts() {
  // console.log("Fetching posts on the server...");
  const res = await fetch("https://jsonplaceholder.typicode.com/posts");
  if (!res.ok) throw new Error("Failed to fetch posts");
  return res.json();
}

export default async function ServerFetchedPostsPage() {
  const queryClient = getQueryClient();

  await queryClient.prefetchQuery({
    queryKey: ["serverPosts"],
    queryFn: getPosts,
  });

  const dehydratedState = dehydrate(queryClient);

  return (
    <HydrationBoundary state={dehydratedState}>
      <PostListClient />
    </HydrationBoundary>
  );
}
```

```javascript
// app/server-fetched-posts/PostListClient.jsx (Client Component)
"use client";

import { useQuery } from "@tanstack/react-query";

// This function doesn't need to be async if the data is expected to be hydrated
// It will run on the client if data is stale or for refetches.
async function getPostsClient() {
  // console.log("Fetching posts on the client (if stale/needed)...");
  const res = await fetch("https://jsonplaceholder.typicode.com/posts");
  if (!res.ok) {
    throw new Error("Network response was not ok on client");
  }
  return res.json();
}

export default function PostListClient() {
  const { data, error, isLoading, isFetching } = useQuery({
    queryKey: ["serverPosts"], // Must match the key used in prefetchQuery
    queryFn: getPostsClient, // This will be called on client if data is missing or stale
  });

  if (isLoading && !data) return <p>Loading initial data...</p>; // isLoading is true initially until hydration
  if (error) return <p>An error occurred: {error.message}</p>;

  return (
    <div>
      <h1>Server Fetched & Hydrated Posts</h1>
      {isFetching && (
        <p>
          <em>Client is re-fetching or background updating...</em>
        </p>
      )}
      <ul>
        {data?.slice(0, 10).map((post) => (
          <li key={post.id}>{post.title}</li>
        ))}
      </ul>
    </div>
  );
}
```

**Explanation of Hydration:**

1.  **Server (`page.jsx`):**
    - A `QueryClient` instance is obtained.
    - `prefetchQuery` fetches the data and populates the server-side query client's cache.
    - `dehydrate(queryClient)` serializes the cache data.
    - `HydrationBoundary` makes this dehydrated state available to its children.
2.  **Client (`PostListClient.jsx`):**
    - When `PostListClient` mounts, `useQuery` checks if data for `["serverPosts"]` exists in the cache (provided by `HydrationBoundary`).
    - If found (and not stale beyond its `staleTime`), it uses the hydrated data immediately, avoiding a client-side fetch on initial load.
    - TanStack Query then manages this data as usual (background refetches, etc.).

**7. React Query Devtools:**

The `ReactQueryDevtools` component is already included in the `Providers.jsx` example. They are invaluable for debugging.

**Best Practices & Notes:**

- **Singleton `QueryClient` on Client:** Ensure you're not re-creating the `QueryClient` on every render in your client-side provider. The `useState(() => new QueryClient())` pattern in `Providers.jsx` (or the more advanced `getQueryClient` function) handles this.
- **Separate `QueryClient` for Server Requests:** As shown in `getQueryClient`, for server-side operations (like in Server Components before hydration), use a new `QueryClient` instance for each request to avoid data leaking between users.
- **`staleTime` vs. `gcTime`:**
  - `staleTime`: How long data is considered fresh and won't be refetched automatically on mount or window focus. Default is `0`. For SSR/hydrated data, setting a `staleTime > 0` (e.g., `5 * 60 * 1000` for 5 minutes) is common to prevent immediate refetch on the client.
  - `gcTime` (formerly `cacheTime`): How long inactive query data is kept in memory before being garbage collected. Default is 5 minutes.
- **Error Handling:** Implement proper error boundaries and use the `error` state from `useQuery`.
- **Mutations:** Use `useMutation` for creating, updating, or deleting data. You'll typically invalidate queries (`queryClient.invalidateQueries`) after a successful mutation to refetch relevant data.
- **Official Documentation:** The TanStack Query documentation is excellent and provides more advanced patterns and examples, especially for Next.js App Router.

This guide should give you a solid foundation for integrating TanStack Query v5 into your Next.js App Router project. Remember to adapt file paths and specific configurations to your project structure.
