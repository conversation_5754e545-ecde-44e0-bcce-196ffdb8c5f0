import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";

/**
 * Features section component showcasing key application features
 * @param {Object} props - Component props
 * @param {Array} props.features - Array of feature objects
 * @param {string} props.title - Section title
 * @param {string} props.description - Section description
 */
export default function FeaturesSection({
  title = "Powerful Features for Modern Education",
  description = "Everything you need to create, manage, and deliver exceptional quizzes",
  features = [
    {
      icon: "🤖",
      title: "AI-Powered Generation",
      description: "Upload PDFs or provide topics to generate high-quality quiz questions automatically using advanced AI technology.",
      highlight: "Save 90% of prep time"
    },
    {
      icon: "📝",
      title: "Multiple Question Types",
      description: "Support for multiple choice, true/false, fill-in-the-blanks, matching, and ordering questions with auto-grading.",
      highlight: "5+ question formats"
    },
    {
      icon: "🎯",
      title: "Flexible Delivery Modes",
      description: "Deliver quizzes online, print for paper-based tests, or use interactive classroom display mode.",
      highlight: "3 delivery options"
    },
    {
      icon: "📊",
      title: "Instant Analytics",
      description: "Get detailed insights into student performance, question difficulty, and class-wide statistics.",
      highlight: "Real-time insights"
    },
    {
      icon: "👥",
      title: "Class Management",
      description: "Organize students into classes, assign quizzes to specific groups, and track progress effortlessly.",
      highlight: "Streamlined workflow"
    },
    {
      icon: "🔒",
      title: "Secure & Reliable",
      description: "Built with security in mind, ensuring student data protection and reliable quiz delivery.",
      highlight: "Enterprise-grade security"
    }
  ]
}) {
  return (
    <section className="py-20 lg:py-32 bg-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="max-w-3xl mx-auto text-center mb-16">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground mb-6">
            {title}
          </h2>
          <p className="text-lg text-muted-foreground leading-relaxed">
            {description}
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {features.map((feature, index) => (
            <Card 
              key={index} 
              className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border-border/50 hover:border-primary/20"
            >
              <CardHeader className="text-center pb-4">
                <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
                  {feature.icon}
                </div>
                <CardTitle className="text-xl font-semibold text-foreground mb-2">
                  {feature.title}
                </CardTitle>
                <div className="inline-flex items-center px-3 py-1 rounded-full bg-quiz-primary/10 text-quiz-primary text-xs font-medium">
                  {feature.highlight}
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-muted-foreground leading-relaxed text-center">
                  {feature.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <p className="text-muted-foreground mb-4">
            Ready to transform your teaching experience?
          </p>
          <div className="inline-flex items-center text-quiz-primary font-medium">
            <span>Get started in less than 5 minutes</span>
            <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </div>
        </div>
      </div>
    </section>
  );
}
