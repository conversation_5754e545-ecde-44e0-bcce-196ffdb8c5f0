import { Lexend } from "next/font/google";
import "./globals.css";

const lexend = Lexend({
  variable: "--font-lexend",
  subsets: ["latin"],
  display: "swap",
  weight: ["300", "400", "500", "600", "700"],
});

export const metadata = {
  title: "AI QuizMaker - Intelligent Quiz Creation for Educators",
  description:
    "AI-powered quiz creation platform for private schools and tutoring centers in Uzbekistan. Create, manage, and deliver quizzes with artificial intelligence.",
  keywords:
    "quiz maker, AI education, Uzbekistan schools, online quizzes, educational technology",
  authors: [{ name: "AI QuizMaker Team" }],
  viewport: "width=device-width, initial-scale=1",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en" className={lexend.variable}>
      <body className="font-lexend antialiased bg-background text-foreground">
        {children}
      </body>
    </html>
  );
}
